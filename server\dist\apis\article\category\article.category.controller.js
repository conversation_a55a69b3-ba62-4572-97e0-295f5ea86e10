"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const article_category_service_1 = __importDefault(require("./article.category.service"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const mongoId_validation_middleware_1 = require("../../../middlewares/mongoId-validation.middleware");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
class CategoryController {
    constructor() {
        this.path = '/categories';
        this.categoryService = new article_category_service_1.default();
        this.router = (0, express_1.Router)();
        this.upload = (0, multer_1.default)();
        this.createCategory = async (request, response, next) => {
            try {
                const categoryData = request.body;
                const newCategory = await this.categoryService.createCategory(categoryData);
                response.send({ category: newCategory });
            }
            catch (error) {
                next(error);
            }
        };
        this.upsertCategoryVersion = async (req, res, next) => {
            try {
                const { categoryId, language } = req.params;
                const versionData = req.body;
                if (!categoryId || !language)
                    return next(new http_exception_1.default(400, messages_1.MESSAGES.CATEGORY.MISSING_ID_OR_LANGUAGE));
                const languageEnum = language;
                const updatedCategory = await this.categoryService.upsertCategoryVersion(categoryId, languageEnum, versionData);
                res.send(updatedCategory);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllCategoriesList = async (req, res, next) => {
            try {
                const { language } = req.params;
                const queries = req.query;
                const categories = await this.categoryService.getAllCategoriesList({ ...queries, language });
                res.send(categories);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllCategories = async (req, res, next) => {
            try {
                const queries = req.query;
                const categories = await this.categoryService.getAllCategories(queries);
                res.send(categories);
            }
            catch (error) {
                next(error);
            }
        };
        this.getCategory = async (request, response, next) => {
            try {
                const id = request.params.categoryId;
                const result = await this.categoryService.getCategory(id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getSlugBySlug = async (req, res, next) => {
            const { url, language } = req.params;
            try {
                const category = await this.categoryService.getSlugBySlug(language, url);
                res.send(category);
            }
            catch (error) {
                next(error);
            }
        };
        this.getCategoryByUrl = async (req, res, next) => {
            try {
                const { url, language } = req.params;
                const currentUser = req.user;
                const articles = await this.categoryService.getCategoryByUrl(language, url, currentUser);
                res.send(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.getCategoriesByLanguage = async (request, response, next) => {
            try {
                const { language } = request.params;
                const categories = await this.categoryService.getCategoriesByLanguage(language);
                response.send({ categories });
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteCategoryByLanguageAndId = async (req, res, next) => {
            try {
                const { language, categoryId } = req.params;
                if (!language || !categoryId)
                    return next(new http_exception_1.default(400, messages_1.MESSAGES.CATEGORY.MISSING_ID_OR_LANGUAGE));
                const deletedcategory = await this.categoryService.deleteCategoryByLanguageAndId(language, categoryId);
                res.send({
                    message: `Version with language ${language} deleted successfully from category with ID ${categoryId}.`,
                    deletedcategory,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getOppositeLanguageVersions = async (req, res, next) => {
            try {
                const { language, versionIds } = req.params;
                if (!language || !versionIds)
                    return next(new http_exception_1.default(400, messages_1.MESSAGES.CATEGORY.MISSING_ID_OR_LANGUAGE));
                const versionIdsArray = versionIds.split(',');
                const result = await this.categoryService.getOppositeLanguageVersionsCategory(language, versionIdsArray);
                res.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.convertToNewModel = async (req, res, next) => {
            try {
                if (!req.file)
                    return res.status(400).json({ message: 'No file uploaded' });
                const filePath = req.file.path;
                const result = await this.categoryService.convertToNewModel(filePath);
                res.status(201).json({ convertedCount: result.length, categories: result });
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ message: 'Conversion failed', error: error.message });
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getAllCategories);
        this.router.get(`/:language${this.path}`, validateApiKey_middleware_1.default, this.getAllCategoriesList);
        this.router.get(`${this.path}/:categoryId`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), mongoId_validation_middleware_1.validateMongoIds, cache_middleware_1.validateCache, this.getCategory);
        this.router.get(`${this.path}/:language/all`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.getCategoriesByLanguage);
        this.router.get(`${this.path}/:language/:versionIds`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.validateCache, this.getOppositeLanguageVersions);
        this.router.get(`${this.path}/:language/blog/:url`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getSlugBySlug);
        this.router.get(`${this.path}/:language/category/:url`, validateApiKey_middleware_1.default, this.getCategoryByUrl);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.createCategory);
        this.router.post(`${this.path}/:language/:categoryId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.upsertCategoryVersion);
        this.router.delete(`${this.path}/:language/:categoryId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.deleteCategoryByLanguageAndId);
        this.router.put(`${this.path}/`, authentication_middleware_1.default, this.upload.single('file'), this.convertToNewModel);
    }
}
exports.default = CategoryController;
//# sourceMappingURL=article.category.controller.js.map