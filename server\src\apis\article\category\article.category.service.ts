import * as fs from 'fs';
import path from 'path';

import categoryModel from './article.category.model';
import { CategoryI, CategoryVersion } from './article.category.interfaces';
import { Language } from '../article.interface';
import mongoose from 'mongoose';
import articleModel from '../article.model';
import HttpException from '@/utils/exceptions/http.exception';
import { MESSAGES } from '@/utils/helpers/messages';
import { FilterQuery, Types } from 'mongoose';
import { UserI } from '@/apis/user/user.interfaces';

class CategoryArticleService {
    public async getSlugBySlug(language: string, url: string): Promise<{ slug: string; name: string }> {
        const category = await categoryModel.findOne({ [`versions.${language}.url`]: url }).exec();
        if (!category) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const selectedVersion = category.versions[targetLanguage];
        if (!selectedVersion) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
        return {
            slug: selectedVersion.url,
            name: selectedVersion.name,
        };
    }

    public async createCategory(categoryData: any): Promise<any> {
        const duplicateChecks = Object.keys(categoryData.versions).map(language => ({
            [`versions.${language}.name`]: categoryData.versions[language].name.trim(),
        }));

        const duplicateCategory = await categoryModel.findOne({
            $or: duplicateChecks,
        });

        if (duplicateCategory) throw new HttpException(409, MESSAGES.CATEGORY.ALREADY_EXIST);

        const existingUrls: Set<string> = new Set();
        const existingCategories = await categoryModel.find({});

        existingCategories.forEach(existingCategory => {
            // issue to resolve
            Object.values(existingCategory.versions).forEach((version: any) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });

        const defaultLanguage = Language.ENGLISH;

        const processedVersions: { [key: string]: any } = {};

        for (const [language, versionData] of Object.entries(categoryData.versions)) {
            const version = versionData as any;
            if (!version.language) {
                version.language = defaultLanguage;
            }

            let url = version.url || version.name.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            while (existingUrls.has(url)) {
                count++;
                url = `${url}-${count}`;
            }
            existingUrls.add(url);
            version.url = url;
            processedVersions[language] = version;
        }

        const newCategory = new categoryModel({
            ...categoryData,
            versions: processedVersions,
        });
        const savedCategory = await newCategory.save();

        for (const [language, version] of Object.entries(savedCategory.versions)) {
            const versionData = version as any;
            const newArticles = versionData.articles || [];
            for (const articleId of newArticles) {
                const articleVersion = await articleModel.findOne({ 'versions._id': articleId });

                if (articleVersion) {
                    const targetCategoryVersionIndex = articleVersion.versions.findIndex((ver: any) => ver._id.equals(articleId));

                    if (targetCategoryVersionIndex !== -1) {
                        const targetCategoryVersion = articleVersion.versions[targetCategoryVersionIndex];

                        if (!targetCategoryVersion.category.includes(versionData._id)) {
                            targetCategoryVersion.category.push(versionData._id);
                            await articleVersion.save();
                        }
                    }
                }
            }
        }

        return savedCategory;
    }

    public async getAllCategories(queries: any): Promise<any> {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, language, sortOrder = 'desc', name } = queries;

        const queryConditions: FilterQuery<CategoryI> = {};

        if (language) {
            queryConditions[`versions.${language}`] = { $exists: true };
        }

        if (name) {
            queryConditions[`versions.${language}.name`] = new RegExp(name, 'i');
        }

        let categoriesQuery = categoryModel.find(queryConditions);

        if (sortOrder) {
            categoriesQuery = categoriesQuery.sort({ createdAt: sortOrder === 'asc' ? 1 : -1 });
        }

        if (paginated === 'true') {
            const skip = (pageNumber - 1) * pageSize;
            categoriesQuery = categoriesQuery.skip(skip).limit(pageSize);
        }

        const categories = await categoriesQuery.lean();

        const filteredCategories = categories.map(category => {
            const versions: any = {};
            if (language) {
                if (category.versions[language]) {
                    versions[language] = category.versions[language];
                }
            } else {
                Object.assign(versions, category.versions);
            }

            return {
                _id: category._id,
                versions: versions,
                robotsMeta: category.robotsMeta,
            };
        });

        const totalCategories = await categoryModel.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalCategories / pageSize);

        return {
            pageNumber: Number(pageNumber),
            pageSize: Number(pageSize),
            totalPages,
            totalCategories,
            categoriesData: filteredCategories,
        };
    }

    public async getAllCategoriesList(queries: any): Promise<{ categoriesData: any; totalCategories: number }> {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, language, sortOrder = 'desc', name } = queries;

        const queryConditions: FilterQuery<CategoryI> = {};

        if (language) {
            queryConditions[`versions.${language}`] = { $exists: true };
        }

        if (name) {
            queryConditions[`versions.${language}.name`] = new RegExp(name, 'i');
        }

        let categoriesQuery = categoryModel.find(queryConditions);

        if (sortOrder) {
            categoriesQuery = categoriesQuery.sort({ createdAt: sortOrder === 'asc' ? 1 : -1 });
        }

        if (paginated === 'true') {
            const skip = (pageNumber - 1) * pageSize;
            categoriesQuery = categoriesQuery.skip(skip).limit(pageSize);
        }

        const categories = await categoriesQuery.lean();

        const filteredCategories = categories.map(category => {
            const versions: any = {};
            if (language) {
                if (category.versions[language]) {
                    versions[language] = category.versions[language];
                }
            } else {
                Object.assign(versions, category.versions);
            }

            return {
                _id: category._id,
                versions: versions,
                robotsMeta: category.robotsMeta,
            };
        });

        const totalCategories = await categoryModel.countDocuments(queryConditions);

        return {
            categoriesData: filteredCategories,
            totalCategories,
        };
    }

    public async getCategoriesByLanguage(language: string): Promise<any[]> {
        const categories = await categoryModel.find({ [`versions.${language}`]: { $exists: true } }).lean();
        const filteredCategories = categories
            .map(category => {
                const version = category.versions[language];
                return {
                    _id: category._id,
                    versions: {
                        [language]: {
                            name: version.name,
                            id: version._id,
                        },
                    },
                };
            })
            .filter(category => category.versions[language]); // Filter out categories without the requested language

        return filteredCategories;
    }

    public async addVersionToCategory(categoryId: string, newVersion: any): Promise<any> {
        const existingCategory = await categoryModel.findById(categoryId);
        if (!existingCategory) throw new HttpException(404, 'Category not found');

        const existingUrls: Set<string> = new Set();
        const existingCategories = await categoryModel.find({});
        existingCategories.forEach(category => {
            Object.values(category.versions).forEach((version: any) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });

        if (!newVersion.url) {
            newVersion.url = `${newVersion.name.toLowerCase().replace(/\s+/g, '-')}`;
        }
        let tempUrl = newVersion.url;
        let count = 1;

        while (existingUrls.has(tempUrl)) {
            tempUrl = `${newVersion.url}-${count}`;
            count++;
        }
        newVersion.url = tempUrl;

        // Add the new version to the category
        existingCategory.versions[newVersion.language] = newVersion;

        const updatedCategory = await existingCategory.save();

        return updatedCategory;
    }

    public async updateCategoryByLanguageAndId(language: Language, categoryId: string, updateData: any): Promise<any> {
        const categoryToUpdate = await categoryModel.findById(categoryId);

        if (!categoryToUpdate) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);

        const existingUrls: Set<string> = new Set();

        const existingCategories = await categoryModel.find({});
        existingCategories.forEach(existingCategory => {
            Object.values(existingCategory.versions).forEach((version: any) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });

        let updatedVersion: CategoryVersion | null = null;
        const version = categoryToUpdate.versions[language];

        if (version) {
            const previousVersion = JSON.parse(JSON.stringify(version));
            const titleChanged = updateData.name && updateData.name !== version.name;

            if (titleChanged) {
                const titleExists = await categoryModel.exists({
                    [`versions.${language}.name`]: updateData.name.trim(),
                    _id: { $ne: categoryToUpdate._id },
                });
                if (titleExists) throw new HttpException(409, MESSAGES.CATEGORY.TITLE_ALREADY_EXIST);
            }
            const urlChanged = updateData.url && updateData.url !== version.url;

            if (urlChanged) {
                const urlExists = await categoryModel.exists({ [`versions.${language}.url`]: updateData.url });
                if (urlExists) throw new HttpException(409, MESSAGES.CATEGORY.URL_ALREADY_EXIST);

                version.url = updateData.url || updateData.name.toLowerCase().replace(/\s+/g, '-');
            }
            Object.assign(version, updateData);
            updatedVersion = version;

            const defaultLanguage: Language = Language.ENGLISH;
            if (!version.language) {
                version.language = defaultLanguage;
            }

            // Update the version in the category
            categoryToUpdate.versions[language] = version;
            await categoryToUpdate.save();

            if (updatedVersion && updatedVersion.articles.length >= 0) {
                const previousArticles: string[] = previousVersion.articles.map((id: Types.ObjectId) => id.toString());
                const newArticles: string[] = updateData.articles || [];

                const removedArticles = previousArticles.filter((articleId: string) => !newArticles.includes(articleId));
                const addedArticles = newArticles.filter((articleId: string) => !previousArticles.includes(articleId));

                for (const articleId of addedArticles) {
                    const articleVersion = await articleModel.findOne({ 'versions._id': articleId });

                    if (articleVersion) {
                        const targetCategoryVersionIndex = articleVersion.versions.findIndex((ver: any) => ver._id.equals(articleId));

                        if (targetCategoryVersionIndex !== -1) {
                            const targetCategoryVersion = articleVersion.versions[targetCategoryVersionIndex];

                            if (!targetCategoryVersion.category.includes(updatedVersion._id)) {
                                targetCategoryVersion.category.push(updatedVersion._id);
                                await articleVersion.save();
                            }
                        }
                    }
                }

                for (const articleId of removedArticles) {
                    const articleVersion = await articleModel.findOne({ 'versions._id': new Types.ObjectId(articleId) });

                    if (articleVersion) {
                        const targetCategoryVersionIndex = articleVersion.versions.findIndex((ver: any) => ver._id.equals(articleId));

                        if (targetCategoryVersionIndex !== -1) {
                            const targetCategoryVersion = articleVersion.versions[targetCategoryVersionIndex];

                            if (updatedVersion && targetCategoryVersion.category.includes(updatedVersion._id)) {
                                targetCategoryVersion.category = targetCategoryVersion.category.filter(
                                    (catId: Types.ObjectId) => !catId.equals(updatedVersion?._id),
                                );
                                await articleVersion.save();
                            }
                        }
                    }
                }
            }

            return categoryToUpdate;
        }

        throw new HttpException(404, MESSAGES.CATEGORY.VERSION_NOT_EXIST);
    }

    public async upsertCategoryVersion(categoryId: string, language: Language, versionData: Partial<any>): Promise<any> {
        const existingCategory = await categoryModel.findById(categoryId);
        if (!existingCategory) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);

        const existingUrls: Set<string> = new Set();
        const existingCategories = await categoryModel.find({ _id: { $ne: categoryId } });
        existingCategories.forEach(category => {
            Object.values(category.versions).forEach((version: any) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });

        // Check if version exists for this language
        const versionFound = existingCategory.versions[language] !== undefined;

        if (versionFound) {
            return await this.updateCategoryByLanguageAndId(language, categoryId, versionData);
        } else {
            let url = versionData.url || versionData.name.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            if (existingUrls.has(url)) {
                count = 1;
                while (existingUrls.has(`${url}-${count}`)) {
                    count++;
                }
                url = `${url}-${count}`;
            }
            existingUrls.add(url);

            const defaultLanguage: Language = Language.ENGLISH;
            if (!versionData.language) {
                versionData.language = defaultLanguage;
            }

            const newVersion = {
                language: versionData.language || language,
                name: versionData.name,
                url: url,
                description: versionData.description,
                articles: versionData.articles || [],
                metaTitle: versionData.metaTitle,
                metaDescription: versionData.metaDescription,
                image: versionData.image,
                createdAt: new Date(),
                updatedAt: new Date(),
                _id: new mongoose.Types.ObjectId(),
                canonical: versionData.canonical,
            };

            return await this.addVersionToCategory(categoryId, newVersion);
        }
    }

    public async getCategoryByUrl(language: string, url: string, currentUser: UserI): Promise<any> {
        const categories = await categoryModel
            .aggregate([
                { $match: { [`versions.${language}.url`]: url.toLowerCase() } },
                {
                    $addFields: {
                        currentVersion: { $objectToArray: '$versions' },
                    },
                },
                { $unwind: '$currentVersion' },
                { $match: { 'currentVersion.k': language } },
                { $unwind: { path: '$currentVersion.v.articles', preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: 'articles',
                        let: { articleId: '$currentVersion.v.articles' },
                        pipeline: [
                            { $unwind: '$versions' },
                            { $match: { $expr: { $eq: ['$versions._id', '$$articleId'] } } },
                            {
                                $project: {
                                    id: '$versions._id',
                                    name: '$versions.name',
                                    url: '$versions.url',
                                },
                            },
                        ],
                        as: 'articlesDetails',
                    },
                },
                { $unwind: { path: '$articlesDetails', preserveNullAndEmptyArrays: true } },
                {
                    $group: {
                        _id: {
                            id: '$_id',
                            idCategory: '$currentVersion.v._id',
                            language: '$currentVersion.v.language',
                            url: '$currentVersion.v.url',
                            name: '$currentVersion.v.name',
                            robotsMeta: '$robotsMeta',
                        },
                        articles: {
                            $push: {
                                id: '$articlesDetails.id',
                                name: '$articlesDetails.title',
                                url: '$articlesDetails.url',
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: '$_id.id',
                        versions: {
                            [language]: {
                                language: '$_id.language',
                                idCategory: '$_id.idCategory',
                                name: '$_id.name',
                                url: '$_id.url',
                                articles: {
                                    $cond: {
                                        if: { $eq: ['$articles', [null]] },
                                        then: [],
                                        else: '$articles',
                                    },
                                },
                            },
                        },
                        robotsMeta: '$_id.robotsMeta',
                    },
                },
            ])
            .exec();

        if (categories?.length === 0) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);

        const category = categories[0];

        return {
            _id: category._id,
            versions: category.versions,
            robotsMeta: category.robotsMeta || '',
        };
    }

    public async deleteCategoryByLanguageAndId(language: Language, categoryId: string): Promise<CategoryI | null> {
        try {
            const categoryToDeleteFrom = await categoryModel.findById(categoryId);

            if (!categoryToDeleteFrom) {
                throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
            }

            const versionToDelete = categoryToDeleteFrom.versions[language];
            if (!versionToDelete) {
                throw new HttpException(404, MESSAGES.CATEGORY.VERSION_NOT_EXIST);
            }

            // Remove category references from articles
            for (const articleId of versionToDelete.articles) {
                const articleVersion = await articleModel.findOne({
                    'versions._id': articleId,
                });

                if (!articleVersion) continue;

                const targetArticleVersion = articleVersion.versions.find(ver => ver._id.equals(articleId));

                if (!targetArticleVersion) continue;

                const updatedCategories = targetArticleVersion.category.filter(catId => !catId.equals(versionToDelete._id));
                if (updatedCategories.length !== targetArticleVersion.category.length) {
                    targetArticleVersion.category = updatedCategories;
                    await articleVersion.save();
                }
            }

            // Remove the version from the category
            delete categoryToDeleteFrom.versions[language];
            await categoryToDeleteFrom.save();

            // If no versions left, delete the entire category
            if (Object.keys(categoryToDeleteFrom.versions).length === 0) {
                await categoryModel.deleteOne({ _id: categoryId });
            }

            return categoryToDeleteFrom;
        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR);
        }
    }

    public async getCategory(id: string): Promise<any> {
        try {
            const category = await categoryModel.findById(id).lean();
            if (!category) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
            return category;
        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR);
        }
    }

    public async getOppositeLanguageVersionsCategory(language: string, versionIds: string[]): Promise<any[]> {
        const targetLanguage = language === 'en' ? 'fr' : 'en';

        // Find categories that have versions with the specified IDs
        const categories = await categoryModel
            .find({
                $or: [{ [`versions.${language}._id`]: { $in: versionIds } }, { [`versions.${targetLanguage}._id`]: { $in: versionIds } }],
            })
            .exec();

        if (!categories || categories.length === 0) {
            return [];
        }

        const filteredCategories = categories.map(category => {
            const targetVersion = category.versions[targetLanguage];

            if (targetVersion) {
                return {
                    _id: targetVersion._id,
                    name: targetVersion.name,
                };
            } else {
                return {
                    _id: null,
                    name: 'N/A',
                };
            }
        });

        return filteredCategories;
    }

    public async convertToNewModel(filePath: string): Promise<any[]> {
        const rawData = fs.readFileSync(path.resolve(filePath), 'utf-8');
        const legacyCategories = JSON.parse(rawData); // this should be an array
        const convertedCategories = [];

        for (const legacy of legacyCategories) {
            const versions: Record<string, any> = {};

            if (Array.isArray(legacy.versionscategory)) {
                for (const version of legacy.versionscategory) {
                    const lang = version.language;
                    if (lang) {
                        versions[lang] = {
                            ...version,
                            language: lang,
                        };
                    }
                }
            }

            const newCategory = new categoryModel({
                ...legacy,
                versions,
            });

            // Remove old field before saving
            delete newCategory._doc.versionscategory;

            const saved = await newCategory.save();
            convertedCategories.push(saved);
        }

        return convertedCategories;
    }
}

export default CategoryArticleService;
